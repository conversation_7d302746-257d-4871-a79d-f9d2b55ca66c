/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-indigo-200: oklch(87% 0.065 274.039);
    --color-indigo-300: oklch(78.5% 0.115 274.713);
    --color-indigo-400: oklch(67.3% 0.182 276.935);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-white: #fff;
    --spacing: 0.25rem;
    --breakpoint-xl: 80rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --leading-normal: 1.5;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-3xl: 1.5rem;
    --blur-2xl: 40px;
    --blur-3xl: 64px;
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-3 {
    top: calc(var(--spacing) * -3);
  }
  .-top-40 {
    top: calc(var(--spacing) * -40);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-\[calc\(100\%-13rem\)\] {
    top: calc(100% - 13rem);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-\[calc\(50\%\+3rem\)\] {
    left: calc(50% + 3rem);
  }
  .left-\[calc\(50\%-11rem\)\] {
    left: calc(50% - 11rem);
  }
  .left-\[max\(-7rem\,calc\(50\%-52rem\)\)\] {
    left: max(-7rem, calc(50% - 52rem));
  }
  .left-\[max\(45rem\,calc\(50\%\+8rem\)\)\] {
    left: max(45rem, calc(50% + 8rem));
  }
  .isolate {
    isolation: isolate;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .order-first {
    order: -9999;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-start-2 {
    grid-column-start: 2;
  }
  .container {
    width: 100%;
  }
  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }
  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }
  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }
  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }
  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }
  .-m-1\.5 {
    margin: calc(var(--spacing) * -1.5);
  }
  .-m-2\.5 {
    margin: calc(var(--spacing) * -2.5);
  }
  .-m-3 {
    margin: calc(var(--spacing) * -3);
  }
  .-mx-3 {
    margin-inline: calc(var(--spacing) * -3);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .-my-6 {
    margin-block: calc(var(--spacing) * -6);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .flow-root {
    display: flow-root;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-flex {
    display: inline-flex;
  }
  .aspect-577\/310 {
    aspect-ratio: 577/310;
  }
  .aspect-1155\/678 {
    aspect-ratio: 1155/678;
  }
  .size-0\.5 {
    width: calc(var(--spacing) * 0.5);
    height: calc(var(--spacing) * 0.5);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-\[50px\] {
    height: 50px;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-12 {
    max-height: calc(var(--spacing) * 12);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-144\.5 {
    width: calc(var(--spacing) * 144.5);
  }
  .w-144\.25 {
    width: calc(var(--spacing) * 144.25);
  }
  .w-288\.75 {
    width: calc(var(--spacing) * 288.75);
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-screen-xl {
    max-width: var(--breakpoint-xl);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-auto {
    flex: auto;
  }
  .flex-none {
    flex: none;
  }
  .grow {
    flex-grow: 1;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-30 {
    rotate: 30deg;
  }
  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .list-none {
    list-style-type: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }
  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-3 {
    column-gap: calc(var(--spacing) * 3);
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .gap-x-8 {
    column-gap: calc(var(--spacing) * 8);
  }
  :where(.space-x-5 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }
  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }
  .gap-y-10 {
    row-gap: calc(var(--spacing) * 10);
  }
  .gap-y-16 {
    row-gap: calc(var(--spacing) * 16);
  }
  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }
  :where(.divide-gray-500\/10 > :not(:last-child)) {
    border-color: color-mix(in srgb, oklch(55.1% 0.027 264.364) 10%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-gray-500\/10 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-gray-500) 10%, transparent);
    }
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-t-3xl {
    border-top-left-radius: var(--radius-3xl);
    border-top-right-radius: var(--radius-3xl);
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .bg-\[\#12631f\] {
    background-color: #12631f;
  }
  .bg-amber-600 {
    background-color: var(--color-amber-600);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-indigo-500 {
    background-color: var(--color-indigo-500);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-900 {
    background-color: var(--color-red-900);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/5 {
    background-color: color-mix(in srgb, #fff 5%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/5 {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }
  .bg-white\/60 {
    background-color: color-mix(in srgb, #fff 60%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/60 {
      background-color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }
  .bg-linear-to-r {
    --tw-gradient-position: to right;
  }
  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-r {
      --tw-gradient-position: to right in oklab;
    }
  }
  .bg-linear-to-r {
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-tr {
    --tw-gradient-position: to top right;
  }
  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-tr {
      --tw-gradient-position: to top right in oklab;
    }
  }
  .bg-linear-to-tr {
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-\[\#ff80b5\] {
    --tw-gradient-from: #ff80b5;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#9089fc\] {
    --tw-gradient-to: #9089fc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .fill-current {
    fill: currentcolor;
  }
  .object-contain {
    object-fit: contain;
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-3\.5 {
    padding-inline: calc(var(--spacing) * 3.5);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-36 {
    padding-inline: calc(var(--spacing) * 36);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .py-32 {
    padding-block: calc(var(--spacing) * 32);
  }
  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }
  .pt-14 {
    padding-top: calc(var(--spacing) * 14);
  }
  .text-center {
    text-align: center;
  }
  .font-serif {
    font-family: var(--font-serif);
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-base\/7 {
    font-size: var(--text-base);
    line-height: calc(var(--spacing) * 7);
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-lg\/6 {
    font-size: var(--text-lg);
    line-height: calc(var(--spacing) * 6);
  }
  .text-lg\/8 {
    font-size: var(--text-lg);
    line-height: calc(var(--spacing) * 8);
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-normal {
    --tw-tracking: var(--tracking-normal);
    letter-spacing: var(--tracking-normal);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .text-balance {
    text-wrap: balance;
  }
  .text-pretty {
    text-wrap: pretty;
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-indigo-400 {
    color: var(--color-indigo-400);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-white {
    color: var(--color-white);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .opacity-30 {
    opacity: 30%;
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-gray-900\/10 {
    --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 10%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    .ring-gray-900\/10 {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 10%, transparent);
    }
  }
  .ring-indigo-200 {
    --tw-ring-color: var(--color-indigo-200);
  }
  .ring-white\/10 {
    --tw-ring-color: color-mix(in srgb, #fff 10%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    .ring-white\/10 {
      --tw-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .outline-1 {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .-outline-offset-1 {
    outline-offset: calc(1px * -1);
  }
  .outline-white\/10 {
    outline-color: color-mix(in srgb, #fff 10%, transparent);
  }
  @supports (color: color-mix(in lab, red, red)) {
    .outline-white\/10 {
      outline-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .blur-2xl {
    --tw-blur: blur(var(--blur-2xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .ring-inset {
    --tw-ring-inset: inset;
  }
  @media (hover: hover) {
    .group-hover\:text-gray-600:is(:where(.group):hover *) {
      color: var(--color-gray-600);
    }
  }
  .placeholder\:text-gray-500::placeholder {
    color: var(--color-gray-500);
  }
  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }
  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }
  @media (hover: hover) {
    .hover\:bg-gray-700:hover {
      background-color: var(--color-gray-700);
    }
  }
  @media (hover: hover) {
    .hover\:bg-indigo-400:hover {
      background-color: var(--color-indigo-400);
    }
  }
  @media (hover: hover) {
    .hover\:bg-indigo-500:hover {
      background-color: var(--color-indigo-500);
    }
  }
  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }
  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }
  @media (hover: hover) {
    .hover\:ring-gray-900\/20:hover {
      --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 20%, transparent);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .hover\:ring-gray-900\/20:hover {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 20%, transparent);
      }
    }
  }
  @media (hover: hover) {
    .hover\:ring-indigo-300:hover {
      --tw-ring-color: var(--color-indigo-300);
    }
  }
  .focus\:outline-2:focus {
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }
  .focus\:-outline-offset-2:focus {
    outline-offset: calc(2px * -1);
  }
  .focus\:outline-indigo-500:focus {
    outline-color: var(--color-indigo-500);
  }
  .focus-visible\:outline-2:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }
  .focus-visible\:-outline-offset-4:focus-visible {
    outline-offset: calc(4px * -1);
  }
  .focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
  }
  .focus-visible\:outline-gray-900:focus-visible {
    outline-color: var(--color-gray-900);
  }
  .focus-visible\:outline-indigo-500:focus-visible {
    outline-color: var(--color-indigo-500);
  }
  .focus-visible\:outline-indigo-600:focus-visible {
    outline-color: var(--color-indigo-600);
  }
  @media (width >= 40rem) {
    .sm\:-top-80 {
      top: calc(var(--spacing) * -80);
    }
  }
  @media (width >= 40rem) {
    .sm\:top-\[calc\(100\%-30rem\)\] {
      top: calc(100% - 30rem);
    }
  }
  @media (width >= 40rem) {
    .sm\:left-\[calc\(50\%\+36rem\)\] {
      left: calc(50% + 36rem);
    }
  }
  @media (width >= 40rem) {
    .sm\:left-\[calc\(50\%-30rem\)\] {
      left: calc(50% - 30rem);
    }
  }
  @media (width >= 40rem) {
    .sm\:col-start-2 {
      grid-column-start: 2;
    }
  }
  @media (width >= 40rem) {
    .sm\:col-start-auto {
      grid-column-start: auto;
    }
  }
  @media (width >= 40rem) {
    .sm\:mx-8 {
      margin-inline: calc(var(--spacing) * 8);
    }
  }
  @media (width >= 40rem) {
    .sm\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }
  @media (width >= 40rem) {
    .sm\:mt-16 {
      margin-top: calc(var(--spacing) * 16);
    }
  }
  @media (width >= 40rem) {
    .sm\:mt-20 {
      margin-top: calc(var(--spacing) * 20);
    }
  }
  @media (width >= 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }
  @media (width >= 40rem) {
    .sm\:w-288\.75 {
      width: calc(var(--spacing) * 288.75);
    }
  }
  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }
  @media (width >= 40rem) {
    .sm\:max-w-xl {
      max-width: var(--container-xl);
    }
  }
  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  @media (width >= 40rem) {
    .sm\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  @media (width >= 40rem) {
    .sm\:justify-center {
      justify-content: center;
    }
  }
  @media (width >= 40rem) {
    .sm\:gap-x-10 {
      column-gap: calc(var(--spacing) * 10);
    }
  }
  @media (width >= 40rem) {
    .sm\:gap-y-0 {
      row-gap: calc(var(--spacing) * 0);
    }
  }
  @media (width >= 40rem) {
    .sm\:rounded-b-none {
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  @media (width >= 40rem) {
    .sm\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }
  @media (width >= 40rem) {
    .sm\:px-3\.5 {
      padding-inline: calc(var(--spacing) * 3.5);
    }
  }
  @media (width >= 40rem) {
    .sm\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }
  @media (width >= 40rem) {
    .sm\:py-32 {
      padding-block: calc(var(--spacing) * 32);
    }
  }
  @media (width >= 40rem) {
    .sm\:py-48 {
      padding-block: calc(var(--spacing) * 48);
    }
  }
  @media (width >= 40rem) {
    .sm\:pt-16 {
      padding-top: calc(var(--spacing) * 16);
    }
  }
  @media (width >= 40rem) {
    .sm\:text-center {
      text-align: center;
    }
  }
  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  @media (width >= 40rem) {
    .sm\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  @media (width >= 40rem) {
    .sm\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  @media (width >= 40rem) {
    .sm\:text-sm\/6 {
      font-size: var(--text-sm);
      line-height: calc(var(--spacing) * 6);
    }
  }
  @media (width >= 40rem) {
    .sm\:text-xl\/8 {
      font-size: var(--text-xl);
      line-height: calc(var(--spacing) * 8);
    }
  }
  @media (width >= 40rem) {
    .sm\:ring-1 {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  @media (width >= 40rem) {
    .sm\:ring-gray-900\/10 {
      --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 10%, transparent);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .sm\:ring-gray-900\/10 {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 10%, transparent);
      }
    }
  }
  @media (width >= 40rem) {
    .sm\:before\:flex-1::before {
      content: var(--tw-content);
      flex: 1;
    }
  }
  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }
  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  @media (width >= 48rem) {
    .md\:items-center {
      align-items: center;
    }
  }
  @media (width >= 48rem) {
    .md\:justify-between {
      justify-content: space-between;
    }
  }
  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }
  @media (width >= 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }
  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }
  @media (width >= 64rem) {
    .lg\:max-w-4xl {
      max-width: var(--container-4xl);
    }
  }
  @media (width >= 64rem) {
    .lg\:max-w-lg {
      max-width: var(--container-lg);
    }
  }
  @media (width >= 64rem) {
    .lg\:max-w-none {
      max-width: none;
    }
  }
  @media (width >= 64rem) {
    .lg\:flex-1 {
      flex: 1;
    }
  }
  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  @media (width >= 64rem) {
    .lg\:justify-end {
      justify-content: flex-end;
    }
  }
  @media (width >= 64rem) {
    .lg\:gap-x-12 {
      column-gap: calc(var(--spacing) * 12);
    }
  }
  @media (width >= 64rem) {
    .lg\:rounded-tr-none {
      border-top-right-radius: 0;
    }
  }
  @media (width >= 64rem) {
    .lg\:rounded-bl-3xl {
      border-bottom-left-radius: var(--radius-3xl);
    }
  }
  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  @media (width >= 64rem) {
    .lg\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }
  @media (width >= 64rem) {
    .lg\:py-32 {
      padding-block: calc(var(--spacing) * 32);
    }
  }
  @media (width >= 64rem) {
    .lg\:py-56 {
      padding-block: calc(var(--spacing) * 56);
    }
  }
  @media (width >= 64rem) {
    .lg\:pt-2 {
      padding-top: calc(var(--spacing) * 2);
    }
  }
  @media (width >= 80rem) {
    .xl\:-top-6 {
      top: calc(var(--spacing) * -6);
    }
  }
  :where(.rtl\:space-x-reverse:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) > :not(:last-child)) {
    --tw-space-x-reverse: 1;
  }
  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700 {
      background-color: var(--color-gray-700);
    }
  }
  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-900 {
      background-color: var(--color-gray-900);
    }
  }
  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-300 {
      color: var(--color-gray-300);
    }
  }
  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400 {
      color: var(--color-gray-400);
    }
  }
  @media (prefers-color-scheme: dark) {
    .dark\:text-white {
      color: var(--color-white);
    }
  }
  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-white:hover {
        color: var(--color-white);
      }
    }
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-content: "";
    }
  }
}
